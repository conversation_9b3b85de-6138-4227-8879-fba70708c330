"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { GlassCard, GlassCardContent, GlassCardHeader, GlassCardTitle, GlassCardDescription } from "@/components/ui/glass-card";
import {
  CheckCircle,
  Users,
  Layers,
  Calendar,
  Tag,
  Zap,
  ArrowRight,
  Star,
  Sparkles
} from "lucide-react";
import { cn } from "@/lib/utils";

export function LandingPage() {
  const [mounted, setMounted] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Trigger animations after mount
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className={cn(
      "min-h-screen bg-background transition-opacity duration-1000",
      isVisible ? "opacity-100" : "opacity-0"
    )}>
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 glass-card border-0 border-b border-border/50 rounded-none">
        <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center space-x-2 sm:space-x-3">
              <Image
                src="/NeoTask_Icon_N.webp"
                alt="NeoTask"
                width={28}
                height={28}
                className="object-contain sm:w-8 sm:h-8"
              />
              <span className="text-lg sm:text-xl font-light tracking-tight">NeoTask</span>
            </div>

            {/* Sign Up Button */}
            <Button asChild size="sm" className="gradient-red-purple text-white font-normal sm:size-default">
              <Link href="/signup">
                <span className="hidden sm:inline">Sign Up</span>
                <span className="sm:hidden">Sign Up</span>
                <ArrowRight className="ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-20 sm:pt-24 pb-12 sm:pb-16 px-4 sm:px-6">
        <div className="w-full max-w-4xl mx-auto">
          <div className={cn(
            "text-center space-y-6 sm:space-y-8 transition-all duration-1000 delay-300",
            isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
          )}>
            {/* Logo */}
            <div className="flex justify-center">
              <Image
                src="/NeoTask_Logo_white.webp"
                alt="NeoTask"
                width={100}
                height={100}
                className="object-contain sm:w-[120px] sm:h-[120px]"
                priority
              />
            </div>

            {/* Hero Text */}
            <div className="space-y-3 sm:space-y-4">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-extralight tracking-tight gradient-red-purple gradient-text">
                Task Management
                <br />
                Reimagined
              </h1>
              <p className="text-lg sm:text-xl text-muted-foreground font-light max-w-2xl mx-auto px-4">
                Organize your work with beautiful lists, collaborate seamlessly with your team,
                and stay focused on what matters most.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4">
              <Button asChild size="lg" className="gradient-red-purple text-white font-normal w-full sm:w-auto">
                <Link href="/signup">
                  Get Started Free
                  <Sparkles className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="font-normal w-full sm:w-auto">
                <Link href="/signin">
                  Sign In
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 sm:py-16 px-4 sm:px-6">
        <div className="w-full max-w-6xl mx-auto">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl font-light tracking-tight mb-3 sm:mb-4">Everything you need to stay organized</h2>
            <p className="text-base sm:text-lg text-muted-foreground font-light px-4">
              Powerful features designed to help you and your team work more efficiently
            </p>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {/* Task Management */}
            <GlassCard intensity="medium" className="text-center">
              <GlassCardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-primary/10">
                    <CheckCircle className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <GlassCardTitle>Smart Task Management</GlassCardTitle>
                <GlassCardDescription>
                  Create tasks with subtasks, set due dates, and organize everything with drag-and-drop simplicity.
                </GlassCardDescription>
              </GlassCardHeader>
            </GlassCard>

            {/* Collaboration */}
            <GlassCard intensity="medium" className="text-center">
              <GlassCardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-secondary/10">
                    <Users className="h-8 w-8 text-secondary" />
                  </div>
                </div>
                <GlassCardTitle>Team Collaboration</GlassCardTitle>
                <GlassCardDescription>
                  Share spaces with your team, collaborate on projects, and keep everyone aligned on goals.
                </GlassCardDescription>
              </GlassCardHeader>
            </GlassCard>

            {/* Spaces & Lists */}
            <GlassCard intensity="medium" className="text-center">
              <GlassCardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-accent/10">
                    <Layers className="h-8 w-8 text-accent" />
                  </div>
                </div>
                <GlassCardTitle>Spaces & Lists</GlassCardTitle>
                <GlassCardDescription>
                  Organize projects into spaces and break them down into customizable, color-coded lists.
                </GlassCardDescription>
              </GlassCardHeader>
            </GlassCard>

            {/* Calendar Integration */}
            <GlassCard intensity="medium" className="text-center">
              <GlassCardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-primary/10">
                    <Calendar className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <GlassCardTitle>Calendar View</GlassCardTitle>
                <GlassCardDescription>
                  Visualize your tasks and deadlines with an integrated calendar view that keeps you on track.
                </GlassCardDescription>
              </GlassCardHeader>
            </GlassCard>

            {/* Tags & Filtering */}
            <GlassCard intensity="medium" className="text-center">
              <GlassCardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-secondary/10">
                    <Tag className="h-8 w-8 text-secondary" />
                  </div>
                </div>
                <GlassCardTitle>Smart Tags</GlassCardTitle>
                <GlassCardDescription>
                  Tag your tasks and filter by categories to quickly find what you're looking for.
                </GlassCardDescription>
              </GlassCardHeader>
            </GlassCard>

            {/* Performance */}
            <GlassCard intensity="medium" className="text-center">
              <GlassCardHeader>
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-accent/10">
                    <Zap className="h-8 w-8 text-accent" />
                  </div>
                </div>
                <GlassCardTitle>Lightning Fast</GlassCardTitle>
                <GlassCardDescription>
                  Built for speed with instant updates, offline support, and seamless synchronization.
                </GlassCardDescription>
              </GlassCardHeader>
            </GlassCard>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-12 sm:py-16 px-4 sm:px-6 bg-muted/20">
        <div className="w-full max-w-4xl mx-auto">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl font-light tracking-tight mb-3 sm:mb-4">Why teams choose NeoTask</h2>
            <p className="text-base sm:text-lg text-muted-foreground font-light px-4">
              Join thousands of teams who have transformed their productivity
            </p>
          </div>

          <div className="grid sm:grid-cols-3 gap-6 sm:gap-8">
            <div className="text-center space-y-3 sm:space-y-4">
              <div className="text-3xl sm:text-4xl font-light text-primary">10x</div>
              <h3 className="text-lg sm:text-xl font-normal">Faster Task Creation</h3>
              <p className="text-sm sm:text-base text-muted-foreground px-2">
                Quick-add functionality and smart defaults get you organized in seconds, not minutes.
              </p>
            </div>

            <div className="text-center space-y-3 sm:space-y-4">
              <div className="text-3xl sm:text-4xl font-light text-secondary">100%</div>
              <h3 className="text-lg sm:text-xl font-normal">Team Alignment</h3>
              <p className="text-sm sm:text-base text-muted-foreground px-2">
                Real-time collaboration ensures everyone stays on the same page, always.
              </p>
            </div>

            <div className="text-center space-y-3 sm:space-y-4">
              <div className="text-3xl sm:text-4xl font-light text-accent">∞</div>
              <h3 className="text-lg sm:text-xl font-normal">Customization</h3>
              <p className="text-sm sm:text-base text-muted-foreground px-2">
                Adapt NeoTask to your workflow with custom colors, tags, and organization systems.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-12 sm:py-16 px-4 sm:px-6">
        <div className="w-full max-w-4xl mx-auto">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl font-light tracking-tight mb-3 sm:mb-4">Loved by teams worldwide</h2>
          </div>

          <div className="grid md:grid-cols-2 gap-6 sm:gap-8">
            <GlassCard intensity="subtle">
              <GlassCardContent className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <p className="text-lg mb-4 font-light">
                  "NeoTask has completely transformed how our team manages projects. The glass design is beautiful and the functionality is exactly what we needed."
                </p>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-primary to-secondary"></div>
                  <div>
                    <div className="font-medium">Sarah Chen</div>
                    <div className="text-sm text-muted-foreground">Product Manager, TechCorp</div>
                  </div>
                </div>
              </GlassCardContent>
            </GlassCard>

            <GlassCard intensity="subtle">
              <GlassCardContent className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <p className="text-lg mb-4 font-light">
                  "The collaboration features are incredible. We can finally keep track of everything in one place, and the real-time updates keep everyone aligned."
                </p>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-secondary to-accent"></div>
                  <div>
                    <div className="font-medium">Marcus Rodriguez</div>
                    <div className="text-sm text-muted-foreground">Creative Director, DesignStudio</div>
                  </div>
                </div>
              </GlassCardContent>
            </GlassCard>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-16 sm:py-20 px-4 sm:px-6">
        <div className="w-full max-w-4xl mx-auto">
          <GlassCard intensity="strong" className="text-center p-6 sm:p-12">
            <GlassCardContent>
              <div className="space-y-4 sm:space-y-6">
                <h2 className="text-2xl sm:text-3xl font-light tracking-tight gradient-red-purple gradient-text">
                  Ready to transform your productivity?
                </h2>
                <p className="text-lg sm:text-xl text-muted-foreground font-light max-w-2xl mx-auto px-4">
                  Join thousands of teams who have already made the switch to smarter task management.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center">
                  <Button asChild size="lg" className="gradient-red-purple text-white font-normal w-full sm:w-auto">
                    <Link href="/signup">
                      Start Free Today
                      <Sparkles className="ml-2 h-5 w-5" />
                    </Link>
                  </Button>
                  <p className="text-xs sm:text-sm text-muted-foreground text-center">
                    No credit card required • Free forever plan available
                  </p>
                </div>
              </div>
            </GlassCardContent>
          </GlassCard>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-6 sm:py-8 px-4 sm:px-6 border-t border-border/50">
        <div className="w-full max-w-4xl mx-auto">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <Image
                src="/NeoTask_Icon_N.webp"
                alt="NeoTask"
                width={20}
                height={20}
                className="object-contain sm:w-6 sm:h-6"
              />
              <span className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
                © 2024 NeoTask. Built with modern web technologies.
              </span>
            </div>
            <div className="flex items-center space-x-4 sm:space-x-6">
              <Link href="/signin" className="text-xs sm:text-sm text-muted-foreground hover:text-foreground transition-colors">
                Sign In
              </Link>
              <Link href="/signup" className="text-xs sm:text-sm text-muted-foreground hover:text-foreground transition-colors">
                Sign Up
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
